@echo off
echo Building CoinHack mod for BLOCKPOST...

REM التحقق من وجود dotnet
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET SDK is not installed or not in PATH
    echo Please install .NET 6.0 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM تجميع المشروع
echo Compiling the mod...
dotnet build --configuration Release

if %errorlevel% equ 0 (
    echo Build successful!
    
    REM نسخ الملف المجمع إلى مجلد plugins
    if exist "bin\Release\net6.0\CoinHack.dll" (
        copy "bin\Release\net6.0\CoinHack.dll" "BepInEx\plugins\"
        echo Mod installed successfully to BepInEx\plugins\
        echo.
        echo Instructions:
        echo 1. Start BLOCKPOST
        echo 2. Enter a game
        echo 3. Press F1 to add 1000 coins
        echo 4. Press F2 to reset (allows adding coins again)
        echo.
    ) else (
        echo Error: Could not find compiled DLL file
    )
) else (
    echo Build failed! Check the errors above.
)

pause
