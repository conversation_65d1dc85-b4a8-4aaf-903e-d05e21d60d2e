using BepInEx;
using BepInEx.IL2CPP;
using HarmonyLib;
using UnhollowerRuntimeLib;
using UnityEngine;
using System;

namespace CoinHack
{
    [BepInPlugin(PluginInfo.PLUGIN_GUID, PluginInfo.PLUGIN_NAME, PluginInfo.PLUGIN_VERSION)]
    public class Plugin : BasePlugin
    {
        public const string PluginGuid = "com.coinhack.blockpost";
        public const string PluginName = "CoinHack";
        public const string PluginVersion = "1.0.0";

        public override void Load()
        {
            Log.LogInfo($"Plugin {PluginName} is loaded!");
            
            // تسجيل الأنواع المطلوبة
            ClassInjector.RegisterTypeInIl2Cpp<CoinManager>();
            
            // إضافة مكون إدارة العملات إلى GameObject
            var gameObject = new GameObject("CoinHackManager");
            gameObject.AddComponent<CoinManager>();
            UnityEngine.Object.DontDestroyOnLoad(gameObject);
            
            Log.LogInfo("CoinHack Manager created successfully!");
        }
    }

    public class CoinManager : MonoBehaviour
    {
        private bool coinsAdded = false;
        
        void Start()
        {
            Log.LogInfo("CoinManager started!");
        }
        
        void Update()
        {
            // التحقق من الضغط على مفتاح F1 لإضافة العملات
            if (Input.GetKeyDown(KeyCode.F1) && !coinsAdded)
            {
                AddCoins();
            }
            
            // إعادة تعيين العملات بالضغط على F2
            if (Input.GetKeyDown(KeyCode.F2))
            {
                coinsAdded = false;
                Log.LogInfo("Coins reset flag cleared. Press F1 to add coins again.");
            }
        }
        
        private void AddCoins()
        {
            try
            {
                // البحث عن مدير اللعبة أو نظام العملات
                // هذه أسماء شائعة لمديري اللعبة في Unity
                var gameManager = FindObjectOfType<MonoBehaviour>();
                
                if (gameManager != null)
                {
                    // محاولة العثور على متغيرات العملات الشائعة
                    var fields = gameManager.GetType().GetFields();
                    
                    foreach (var field in fields)
                    {
                        // البحث عن حقول العملات المحتملة
                        if (field.Name.ToLower().Contains("coin") || 
                            field.Name.ToLower().Contains("money") || 
                            field.Name.ToLower().Contains("currency") ||
                            field.Name.ToLower().Contains("gold") ||
                            field.Name.ToLower().Contains("cash"))
                        {
                            if (field.FieldType == typeof(int))
                            {
                                int currentValue = (int)field.GetValue(gameManager);
                                field.SetValue(gameManager, currentValue + 1000);
                                Log.LogInfo($"Added 1000 coins to {field.Name}! New value: {currentValue + 1000}");
                                coinsAdded = true;
                                return;
                            }
                            else if (field.FieldType == typeof(float))
                            {
                                float currentValue = (float)field.GetValue(gameManager);
                                field.SetValue(gameManager, currentValue + 1000f);
                                Log.LogInfo($"Added 1000 coins to {field.Name}! New value: {currentValue + 1000f}");
                                coinsAdded = true;
                                return;
                            }
                        }
                    }
                }
                
                // إذا لم نجد نظام العملات، نحاول طريقة أخرى
                TryAlternativeMethod();
            }
            catch (Exception ex)
            {
                Log.LogError($"Error adding coins: {ex.Message}");
            }
        }
        
        private void TryAlternativeMethod()
        {
            try
            {
                // محاولة العثور على جميع MonoBehaviour في المشهد
                var allObjects = FindObjectsOfType<MonoBehaviour>();
                
                foreach (var obj in allObjects)
                {
                    var type = obj.GetType();
                    var fields = type.GetFields();
                    
                    foreach (var field in fields)
                    {
                        if ((field.Name.ToLower().Contains("coin") || 
                             field.Name.ToLower().Contains("money") ||
                             field.Name.ToLower().Contains("score")) && 
                            (field.FieldType == typeof(int) || field.FieldType == typeof(float)))
                        {
                            if (field.FieldType == typeof(int))
                            {
                                int currentValue = (int)field.GetValue(obj);
                                field.SetValue(obj, currentValue + 1000);
                                Log.LogInfo($"Found and updated {type.Name}.{field.Name}! Added 1000 coins.");
                                coinsAdded = true;
                                return;
                            }
                            else if (field.FieldType == typeof(float))
                            {
                                float currentValue = (float)field.GetValue(obj);
                                field.SetValue(obj, currentValue + 1000f);
                                Log.LogInfo($"Found and updated {type.Name}.{field.Name}! Added 1000 coins.");
                                coinsAdded = true;
                                return;
                            }
                        }
                    }
                }
                
                Log.LogWarning("Could not find coin system. Make sure you're in the game and try again.");
            }
            catch (Exception ex)
            {
                Log.LogError($"Alternative method failed: {ex.Message}");
            }
        }
    }
    
    public static class PluginInfo
    {
        public const string PLUGIN_GUID = "com.coinhack.blockpost";
        public const string PLUGIN_NAME = "CoinHack";
        public const string PLUGIN_VERSION = "1.0.0";
    }
}
