<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AssemblyTitle>CoinHack</AssemblyTitle>
    <AssemblyDescription>BLOCKPOST Coin Hack Mod</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>false</UseWindowsForms>
    <ImportWindowsDesktopTargets>false</ImportWindowsDesktopTargets>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BepInEx.IL2CPP" Version="6.0.0-be.674" />
    <PackageReference Include="BepInEx.PluginInfoProps" Version="2.1.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Il2Cppmscorlib">
      <HintPath>BepInEx\unhollowed\Il2Cppmscorlib.dll</HintPath>
    </Reference>
    <Reference Include="Il2CppSystem">
      <HintPath>BepInEx\unhollowed\Il2CppSystem.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine">
      <HintPath>BepInEx\unhollowed\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>BepInEx\unhollowed\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnhollowerBaseLib">
      <HintPath>BepInEx\core\UnhollowerBaseLib.dll</HintPath>
    </Reference>
    <Reference Include="UnhollowerRuntimeLib">
      <HintPath>BepInEx\core\UnhollowerRuntimeLib.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>
