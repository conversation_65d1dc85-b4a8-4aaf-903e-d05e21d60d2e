# BLOCKPOST Coin Hack Mod

## الوصف
هذا الـ mod يضيف 1000 عملة إلى حسابك في لعبة BLOCKPOST عند الضغط على مفتاح F1.

## المتطلبات
- لعبة BLOCKPOST مثبتة
- BepInEx مثبت (موجود بالفعل في مجلدك)
- .NET 6.0 SDK للتجميع

## طريقة التثبيت

### الطريقة الأولى: التجميع التلقائي
1. تأكد من تثبيت .NET 6.0 SDK من الموقع الرسمي
2. اضغط مرتين على ملف `build.bat`
3. انتظر حتى ينتهي التجميع
4. سيتم نسخ الملف تلقائياً إلى مجلد BepInEx\plugins

### الطريقة الثانية: التجميع اليدوي
```bash
dotnet build --configuration Release
copy "bin\Release\net6.0\CoinHack.dll" "BepInEx\plugins\"
```

## طريقة الاستخدام
1. شغل لعبة BLOCKPOST
2. ادخل إلى أي لعبة أو وضع
3. اضغط على مفتاح **F1** لإضافة 1000 عملة
4. اضغط على مفتاح **F2** لإعادة تعيين العداد (يسمح بإضافة العملات مرة أخرى)

## الملفات المتضمنة
- `CoinHack.cs` - الكود الرئيسي للـ mod (نسخة متقدمة)
- `SimpleCoinHack.cs` - نسخة مبسطة من الـ mod
- `CoinHack.csproj` - ملف المشروع للتجميع
- `build.bat` - سكريبت التجميع التلقائي

## استكشاف الأخطاء
- إذا لم يعمل الـ mod، تحقق من ملف `BepInEx\LogOutput.log` للأخطاء
- تأكد من أن BepInEx يعمل بشكل صحيح
- جرب النسخة المبسطة `SimpleCoinHack.cs` إذا لم تعمل النسخة الرئيسية

## ملاحظات مهمة
- هذا الـ mod للاستخدام الشخصي فقط
- قد لا يعمل في الألعاب الجماعية أو الخوادم المحمية
- استخدم على مسؤوليتك الخاصة

## الدعم
إذا واجهت أي مشاكل، تحقق من:
1. أن BepInEx مثبت بشكل صحيح
2. أن .NET SDK مثبت
3. أن الملف موجود في مجلد BepInEx\plugins
4. ملف السجل للأخطاء
