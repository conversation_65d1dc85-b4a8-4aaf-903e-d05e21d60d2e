using BepInEx;
using BepInEx.IL2CPP;
using UnityEngine;
using System;
using System.Reflection;

[BepInPlugin("com.simple.coinhack", "Simple Coin Hack", "1.0.0")]
public class SimpleCoinHack : BasePlugin
{
    private bool hasAddedCoins = false;
    
    public override void Load()
    {
        Log.LogInfo("Simple Coin Hack loaded! Press F1 in game to add 1000 coins.");
    }
    
    public void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1))
        {
            AddCoins();
        }
    }
    
    private void AddCoins()
    {
        try
        {
            // البحث عن جميع الكائنات في المشهد
            GameObject[] allObjects = UnityEngine.Object.FindObjectsOfType<GameObject>();
            
            foreach (GameObject obj in allObjects)
            {
                // البحث عن المكونات التي قد تحتوي على معلومات العملات
                MonoBehaviour[] components = obj.GetComponents<MonoBehaviour>();
                
                foreach (MonoBehaviour component in components)
                {
                    if (component == null) continue;
                    
                    Type componentType = component.GetType();
                    FieldInfo[] fields = componentType.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    
                    foreach (FieldInfo field in fields)
                    {
                        string fieldName = field.Name.ToLower();
                        
                        // البحث عن حقول العملات المحتملة
                        if (fieldName.Contains("coin") || fieldName.Contains("money") || 
                            fieldName.Contains("gold") || fieldName.Contains("cash") ||
                            fieldName.Contains("currency") || fieldName.Contains("score"))
                        {
                            try
                            {
                                if (field.FieldType == typeof(int))
                                {
                                    int currentValue = (int)field.GetValue(component);
                                    field.SetValue(component, currentValue + 1000);
                                    Log.LogInfo($"Added 1000 to {componentType.Name}.{field.Name}! New value: {currentValue + 1000}");
                                    return;
                                }
                                else if (field.FieldType == typeof(float))
                                {
                                    float currentValue = (float)field.GetValue(component);
                                    field.SetValue(component, currentValue + 1000f);
                                    Log.LogInfo($"Added 1000 to {componentType.Name}.{field.Name}! New value: {currentValue + 1000f}");
                                    return;
                                }
                            }
                            catch (Exception ex)
                            {
                                // تجاهل الأخطاء والمتابعة
                                continue;
                            }
                        }
                    }
                }
            }
            
            Log.LogWarning("Could not find coin system. Make sure you're in a game!");
        }
        catch (Exception ex)
        {
            Log.LogError($"Error in AddCoins: {ex.Message}");
        }
    }
}
